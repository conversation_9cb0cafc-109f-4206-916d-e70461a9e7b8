import { AuthProvider } from '@propelauth/react'

const propelAuthUrl = import.meta.env.VITE_PROPELAUTH_URL

if (!propelAuthUrl) {
  throw new Error('Missing VITE_PROPELAUTH_URL environment variable')
}

// Ensure the URL doesn't have a trailing slash
const cleanAuthUrl = propelAuthUrl.replace(/\/$/, '')

// Log the auth URL for debugging
console.log('PropelAuth URL:', cleanAuthUrl)

export { AuthProvider }
export const authUrl = cleanAuthUrl
