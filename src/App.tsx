import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from '@propelauth/react'
import { DashboardPage } from './pages/DashboardPage'
import { authUrl } from './services/propelauth'
import { AuthErrorBoundary } from './components/auth/AuthErrorBoundary'

function App() {
  return (
    <AuthErrorBoundary>
      <AuthProvider authUrl={authUrl}>
        <Router>
          <Routes>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </AuthErrorBoundary>
  )
}

export default App
