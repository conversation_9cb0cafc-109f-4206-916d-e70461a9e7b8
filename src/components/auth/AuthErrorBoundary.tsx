import React, { Component, ReactNode } from 'react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Auth Error:', error, errorInfo)
    
    // Check if it's a PropelAuth related error
    if (error.message.includes('refresh_token') || error.message.includes('401')) {
      console.log('PropelAuth token refresh failed - clearing storage')
      // Clear any stored auth data
      localStorage.clear()
      sessionStorage.clear()
    }
  }

  handleRetry = () => {
    // Clear storage and reload
    localStorage.clear()
    sessionStorage.clear()
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="auth-error-boundary">
          <div className="container">
            <div className="auth-container">
              <h2>Authentication Error</h2>
              <p>There was an issue with authentication. This usually happens when your session has expired.</p>
              
              <div className="auth-error-details">
                <p><strong>Error:</strong> {this.state.error?.message}</p>
              </div>

              <button 
                onClick={this.handleRetry}
                className="btn btn-primary btn-full"
              >
                Clear Session & Retry
              </button>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
